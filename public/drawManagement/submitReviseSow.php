<?php
use models\portals\PublicPage;
use models\JSCompiler;
use models\standard\Strings;
use models\composite\oDrawManagement\SowTemplateManager;
use models\lendingwise\tblDrawRequests;
use models\composite\oDrawManagement\BorrowerDrawRequest;
use models\cypher;
use models\Request;

session_start();
require '../includes/util.php';
$userType = 'borrower';
$pcid = Request::GetClean('pcid') ?? 0;
if (!is_numeric($pcid)) {
    $pcid = cypher::myDecryption($pcid);
}
$fileid = Request::GetClean('lmrid');
if (!is_numeric($fileid)) {
    $fileid = (int)cypher::myDecryption($fileid);
}
$templateManager = SowTemplateManager::forProcessingCompany($pcid);
$templateData = $templateManager->getTemplateDataArray();

$drawRequest = tblDrawRequests::Get(['LMRId' => $fileid]);

if($drawRequest) {
    if(!$drawRequest->isDrawRequest) {
        switch($drawRequest->status) {
            case BorrowerDrawRequest::STATUS_PENDING:
                $displayStatus = 'Your scope of work has been submitted and is awaiting approval by the lender.';
                $displayStatusClass = 'alert-warning';
                break;
            case BorrowerDrawRequest::STATUS_APPROVED:
                $displayStatus = 'Scope of work approved.';
                $displayStatusClass = 'alert-success';
                break;
            case BorrowerDrawRequest::STATUS_REJECTED:
                $displayStatus = 'Please see revision requests under lender notes, revise, and resubmit.';
                $displayStatusClass = 'alert-danger';
                break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link href="/assets/images/favicon-whitelabel.png" rel="SHORTCUT ICON" />
    <title> Submit/Revise Scope of Work</title>
    <?php
    PublicPage::Init();
    echo JSCompiler::scripts();
    echo JSCompiler::stylesheets();
    if(!$drawRequest->isDrawRequest) {
        Strings::includeMyScript(['/assets/js/drawManagement/common.js']);
        Strings::includeMyScript(['/assets/js/drawManagement/borrower.js']);
        Strings::includeMyScript(['/assets/js/3rdParty/sortable/Sortable.min.js']);
        Strings::includeMyCSS(['/assets/css/components/drawManagement.css']);
    }
    ?>
</head>

<body translate="no">

    <style>
    html {
        font-size: 12px;
    }

    body {
        background-color: #f8f9fa;
        font-size: 1.2rem;
    }
    .table th {
        font-size: 1rem;
    }

    .table th,
    .table td {
        vertical-align: middle;
        border: none !important;
    }

    .table td {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .categories-container i,
    .content-step-line-items i {
        color: #b5b5c3;
    }

    .line-item-display {
        font-size: 1.1rem;
        padding-left: 5px;
    }

    .text-success {
        color: #1bc5bd !important
    }

    .font-weight-bold {
        font-weight: 400 !important;
    }

    .note-btn {
        color: #3699ff;
    }

    .note-btn[data-note=""] {
        color: #ccc !important;
    }

    /* --- Custom Popover Styles --- */
    .note-container {
        position: relative;
        display: inline-block;
    }

    .popover {
        position: absolute;
        width: max-content;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 10px;
        background-color: #fff;
        color: #212529;
        border: 1px solid #d3d3d3;
        border-radius: 0.25rem;
        padding: 0.5rem 0.75rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        z-index: 1060;
        display: none;
        font-size: 0.875rem;
        white-space: pre-line;
    }

    .popover::before {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%;
        transform: translateY(-50%);
        border-width: 0.4rem;
        border-style: solid;
        border-color: transparent #fff transparent transparent;
    }

    .popover::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%;
        transform: translateY(-50%);
        border-width: 0.4rem;
        border-style: solid;
        border-color: transparent #d3d3d3 transparent transparent;
        margin-right: 1px;
        z-index: -1;
    }

    #toast-container {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .card,
    .line-item-table {
        -webkit-box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
        box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
        border: 0;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid #f1f1f1;
    }
    </style>
    <div class="container mt-4 mb-5">
        <!-- Main Content Card -->
        <div class="card p-4 p-md-5 shadow-sm">
            <?php require 'drawRequest.php'; ?>
            <input type="hidden" id="lmrid" value="<?php echo htmlspecialchars($_REQUEST['lmrid'] ?? ''); ?>">
        </div>
    </div>

    <script>
    $(document).ready(function() {
        DrawManagement.init(`<?=$userType ?>`, `<?= json_encode($templateData) ?>`);
    });
    </script>
</body>

</html>
