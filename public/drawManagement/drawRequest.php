<?php
use models\composite\oDrawManagement\BorrowerDrawRequest;

$borrowerDrawRequest = null;
$categoriesData = [];

if($drawRequest && $drawRequest->id) {
    $borrowerDrawRequest = new BorrowerDrawRequest($drawRequest);
    $drawRequestData = $borrowerDrawRequest->toArray();
    $categoriesData = $drawRequestData['categories'] ?? [];
}
?>
<style>
.card {
    padding: 0 !important;
}
.tooltipClass {
  position: relative;
  display: inline-block;
}

/* Tooltip text */
.tooltipClass .text{
  font-size: 0.8rem;
  text-transform: lowercase;
  letter-spacing: 0.07rem;
  visibility: hidden;
  background-color: #fff;
  color: #000;
  text-align: center;
  padding: 10px;
  border-radius: 4px;
  position: absolute;
  top: -5px;
  left: 105%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  z-index: 1;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltipClass:hover .text {
  visibility: visible;
}

/* Notes button styling */
.note-btn {
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.note-btn:hover {
    transform: scale(1.1);
}

.note-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Popover styling */
.popover {
    max-width: 300px;
}

.popover-body {
    word-wrap: break-word;
}
</style>

<div class="card-body">
    <?php if(isset($displayStatus)) { ?>
        <div class="alert <?= $displayStatusClass; ?>" role="alert">
            <i class="fas fa-info-circle"></i>
            <?= $displayStatus; ?>
        </div>
    <?php } ?>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="mb-0 text-success">Manage Draw Request</h4>
        </div>
    </div>

    <!-- Draw Request Table -->
    <?php if (!empty($categoriesData)): ?>
        <table class="table line-item-table table-bordered">
            <thead class="thead-light">
                <colgroup>
                    <col style="width:25%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:10%">
                    <col style="width:30%">
                    <col style="width:6%">
                </colgroup>
                <tr>
                    <th scope="col">Line Item</th>
                    <th scope="col">Total Budget</th>
                    <th scope="col">Completed Renovations</th>
                    <th scope="col">Prv. Disbursed Amount</th>
                    <th scope="col">% Completed</th>
                    <th scope="col">Requested Amount</th>
                    <th scope="col">Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categoriesData as $category): ?>
                    <?php if (!empty($category['lineItems'])): ?>
                        <!-- Category Header -->
                        <tr class="category-header">
                            <td colspan="7" class="font-weight-bold bg-light">
                                <?= htmlspecialchars(strtoupper($category['name'])) ?>
                                <?php if (!empty($category['description'])): ?>
                                    <i class="fa fa-info-circle text-primary ml-2 tooltipClass"><span class="text"><?= htmlspecialchars($category['description']) ?></span></i>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <!-- Line Items -->
                        <?php foreach ($category['lineItems'] as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem['name']) ?>
                                    <?php if (!empty($lineItem['description'])): ?>
                                        <i class="fa fa-info-circle text-primary ml-2 tooltipClass"><span class="text"><?= htmlspecialchars($lineItem['description']) ?></span></i>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= number_format($lineItem['cost'], 2) ?></td>
                                <td>$<?= number_format($lineItem['completedAmount'], 2) ?></td>
                                <td>$0.00</td>
                                <td><?= round($lineItem['completedPercent']) ?>%</td>
                                <td>
                                    <div class="input-group ">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">%</span>
                                        </div>
                                        <input type="number"
                                           class="form-control"
                                           min="0"
                                           max="100"
                                           value="0"
                                           data-line-item-id="<?= $lineItem['id'] ?>" style="max-width: 70px;"/>
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">$</span>
                                        </div>
                                        <input type="number"
                                           class="form-control"
                                           min="0"
                                           value="0"
                                           data-line-item-id="<?= $lineItem['id'] ?>"/>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="note-container">
                                        <button type="button" class="btn p-0 note-btn"
                                            data-toggle="modal"
                                            data-target="#noteModal"
                                            data-note="<?= htmlspecialchars($lineItem['notes']) ?>"
                                            data-line-item-id="<?= $lineItem['id'] ?>"
                                            title="Notes">
                                            <i class="icon-md fas fa-comment-medical fa-lg"></i>
                                        </button>
                                        <div class="popover" style="display: none;"><?= htmlspecialchars($lineItem['notes']) ?></div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle"></i>
            No draw request data available.
        </div>
    <?php endif; ?>

    <div class="d-flex justify-content-center btn-sm action-buttons">
        <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Submit</button>
    </div>

</div>

<!-- Notes Modal -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-labelledby="noteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noteModalLabel">Edit Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea id="noteTextarea" class="form-control" rows="4" placeholder="Enter notes…"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Cancel</button>
                <button type="button" id="saveNoteBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#noteModal').on('show.bs.modal', function (event) {
        currentNoteBtn = $(event.relatedTarget);
        const noteText = currentNoteBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    $('#saveNoteBtn').on('click', function () {
        const updatedNote = $('#noteTextarea').val();
        currentNoteBtn.data('note', updatedNote);
        currentNoteBtn.attr('data-note', updatedNote);
        $('#noteModal').modal('hide');
        DrawManagement.config.lineItemsModified = true;
        DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
    });
});
</script>
